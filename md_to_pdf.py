#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown to PDF Converter
将Markdown文件转换为PDF格式的工具

支持功能：
- 中文字体支持
- 代码高亮
- 表格格式化
- 图片嵌入
- 自定义样式
"""

import os
import sys
import argparse
import markdown
from pathlib import Path
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import re
from html import unescape


class MarkdownToPDFConverter:
    """Markdown转PDF转换器"""
    
    def __init__(self):
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """设置中文字体"""
        try:
            # 尝试注册系统中的中文字体
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',
                '/System/Library/Fonts/Helvetica.ttc',
                '/Library/Fonts/Arial Unicode MS.ttf',
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        self.chinese_font = 'ChineseFont'
                        break
                    except:
                        continue
            else:
                # 如果没有找到中文字体，使用默认字体
                self.chinese_font = 'Helvetica'
        except:
            self.chinese_font = 'Helvetica'
    
    def setup_styles(self):
        """设置样式"""
        self.styles = getSampleStyleSheet()
        
        # 标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseTitle',
            parent=self.styles['Title'],
            fontName=self.chinese_font,
            fontSize=24,
            spaceAfter=20,
            textColor=colors.darkblue
        ))
        
        # 二级标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseHeading1',
            parent=self.styles['Heading1'],
            fontName=self.chinese_font,
            fontSize=18,
            spaceAfter=12,
            textColor=colors.darkred
        ))
        
        # 三级标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseHeading2',
            parent=self.styles['Heading2'],
            fontName=self.chinese_font,
            fontSize=16,
            spaceAfter=10,
            textColor=colors.darkorange
        ))
        
        # 四级标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseHeading3',
            parent=self.styles['Heading3'],
            fontName=self.chinese_font,
            fontSize=14,
            spaceAfter=8,
            textColor=colors.purple
        ))
        
        # 正文样式
        self.styles.add(ParagraphStyle(
            name='ChineseNormal',
            parent=self.styles['Normal'],
            fontName=self.chinese_font,
            fontSize=12,
            spaceAfter=6,
            leading=18
        ))
        
        # 代码样式
        self.styles.add(ParagraphStyle(
            name='ChineseCode',
            parent=self.styles['Code'],
            fontName='Courier',
            fontSize=10,
            spaceAfter=6,
            backColor=colors.lightgrey
        ))
    
    def parse_markdown_to_elements(self, md_content: str):
        """解析Markdown内容为PDF元素"""
        # 转换为HTML
        md = markdown.Markdown(extensions=[
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.toc'
        ])
        html_content = md.convert(md_content)
        
        # 解析HTML为PDF元素
        elements = []
        
        # 简单的HTML解析
        lines = html_content.split('\n')
        current_text = ""
        
        for line in lines:
            line = line.strip()
            if not line:
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                continue
            
            # 处理标题
            if line.startswith('<h1>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                title = re.sub(r'<[^>]+>', '', line)
                title = unescape(title)
                elements.append(Paragraph(title, self.styles['ChineseTitle']))
                elements.append(Spacer(1, 12))
                
            elif line.startswith('<h2>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                title = re.sub(r'<[^>]+>', '', line)
                title = unescape(title)
                elements.append(Paragraph(title, self.styles['ChineseHeading1']))
                elements.append(Spacer(1, 8))
                
            elif line.startswith('<h3>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                title = re.sub(r'<[^>]+>', '', line)
                title = unescape(title)
                elements.append(Paragraph(title, self.styles['ChineseHeading2']))
                elements.append(Spacer(1, 6))
                
            elif line.startswith('<h4>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                title = re.sub(r'<[^>]+>', '', line)
                title = unescape(title)
                elements.append(Paragraph(title, self.styles['ChineseHeading3']))
                elements.append(Spacer(1, 4))
                
            elif line.startswith('<p>'):
                text = re.sub(r'<[^>]+>', '', line)
                text = unescape(text)
                if text:
                    current_text += text + " "
                    
            elif line.startswith('<code>') or line.startswith('<pre>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                code = re.sub(r'<[^>]+>', '', line)
                code = unescape(code)
                elements.append(Paragraph(code, self.styles['ChineseCode']))
                elements.append(Spacer(1, 6))
                
            elif not line.startswith('<'):
                # 普通文本
                text = unescape(line)
                if text:
                    current_text += text + " "
        
        # 处理剩余文本
        if current_text:
            elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
        
        return elements
    
    def create_pdf(self, elements, output_file: str) -> bool:
        """创建PDF文档"""
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(
                output_file,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # 构建PDF
            doc.build(elements)
            return True
            
        except Exception as e:
            raise Exception(f"创建PDF失败: {str(e)}")
    
    def convert(self, input_file: str, output_file: str = None) -> str:
        """转换Markdown文件为PDF"""
        # 检查输入文件
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 确定输出文件名
        if not output_file:
            input_path = Path(input_file)
            output_file = str(input_path.with_suffix('.pdf'))
        
        print(f"开始转换: {input_file} -> {output_file}")
        
        try:
            # 读取Markdown文件
            with open(input_file, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # 解析为PDF元素
            elements = self.parse_markdown_to_elements(md_content)
            
            # 创建PDF
            self.create_pdf(elements, output_file)
            
            print(f"转换完成: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"转换失败: {str(e)}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Markdown to PDF Converter')
    parser.add_argument('input', help='输入的Markdown文件路径')
    parser.add_argument('-o', '--output', help='输出的PDF文件路径')
    
    args = parser.parse_args()
    
    try:
        converter = MarkdownToPDFConverter()
        output_file = converter.convert(args.input, args.output)
        print(f"✅ 转换成功! PDF文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
